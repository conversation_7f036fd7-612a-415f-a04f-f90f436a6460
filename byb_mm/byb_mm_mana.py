import numpy as np
import pandas as pd
import random
import time
from math import tanh
import sys
sys.path.append(r'/home/<USER>/byb_mm/')
from byex.spot.market import Spot
from byex.spot.trade import SpotTrade
import logging
import pytz
import signal
from datetime import datetime
import con_pri
import asyncio
import traceback
import inspect
import config
import json
import os

beijing_tz = pytz.timezone("Asia/Shanghai")

symbol = 'manausdt'


# 自定义时间格式器，确保日志时间是北京时区时间
class BeijingTimeFormatter(logging.Formatter):
    def converter(self, timestamp):
        # 设置时区为北京时间
        tz = beijing_tz
        dt = datetime.fromtimestamp(timestamp, tz)
        return dt.timetuple()

    def format(self, record):
        # 获取调用者的帧信息
        frame = inspect.currentframe()
        if frame is not None:
            frame = frame.f_back
            while frame:
                # 使用文件名而非__file__变量，以兼容Jupyter环境
                if hasattr(frame, 'f_code') and frame.f_code.co_filename.endswith('byb_mm.py'):
                    record.lineno = frame.f_lineno
                    break
                frame = frame.f_back

        return super().format(record)


logging.basicConfig(
    filename='/home/<USER>/byb_mm/test/byb_mm.log',  # Log to this file
    level=logging.INFO,  # 将日志级别调整为INFO，减少DEBUG日志
    format='%(asctime)s - %(levelname)s - %(message)s',  # 简化格式，移除文件名和行号
    datefmt='%Y-%m-%d %H:%M:%S'  # 指定时间格式
)

# 自定义 Formatter 并应用
logger = logging.getLogger()
for handler in logger.handlers:
    handler.setFormatter(BeijingTimeFormatter('%(asctime)s - %(levelname)s - %(message)s'))


def handle_exit(signum, frame, symbol=symbol):
    logging.info(f"收到信号 {signum}, 进行清理...")
    # 取消所有订单
    try:
        spot_client.cancel_all_orders_by_symbol(symbol)
    except Exception as e:
        logging.error(f"撤单失败: {str(e)}\n{traceback.format_exc()}")
    # 关闭事件循环
    loop = asyncio.get_event_loop()
    loop.stop()
    logging.info("程序即将退出...")
    sys.exit(0)


# 监听 Ctrl+C (SIGINT) 和 kill (SIGTERM)
signal.signal(signal.SIGINT, handle_exit)
signal.signal(signal.SIGTERM, handle_exit)

base_url = "https://openapi.100exdemo.com"
spot_market = Spot()
spot_market.BASE_URL = base_url
spot_client = SpotTrade(con_pri.api_key, con_pri.api_secret)
spot_client.BASE_URL = base_url

# 添加最大并发控制
MAX_CONCURRENT_ORDERS = 10  # 最大并发订单数
order_semaphore = asyncio.Semaphore(MAX_CONCURRENT_ORDERS)  # 订单并发信号量
active_orders = set()  # 活跃订单集合

# 按价格分组的订单ID字典
current_order_ids_grouped = {
    "buy": {},  # 按价格分组的买单订单ID列表
    "sell": {}  # 按价格分组的卖单订单ID列表
}

# 全局变量标记是否是首次运行
first_run = True

# 剩余档位更新控制变量
back_levels_update_counter = 0  # 计数器，用于控制剩余档位更新频率
BACK_LEVELS_UPDATE_INTERVAL = 5  # 剩余档位每5次循环更新一次

# 初始库存量常量
INITIAL_COIN_INVENTORY = 9999017.4656  # 初始 USDT 库存量
INITIAL_USDT_INVENTORY = 1338311.********  # 初始 USD 库存量


def get_assets(coin: str = 'usdt'):
    """获取交易所API返回的原始资产数据"""
    account = spot_client.account()
    df_account = pd.DataFrame(account['coin_list'])
    coin_assets = df_account[df_account['coin'] == coin]
    coin_num = float(coin_assets['normal'].iloc[0]) + float(coin_assets['locked'].iloc[0])
    return coin_num


def get_pending_orders(symbol: str = symbol, max_retries: int = 3):
    """获取当前未成交的挂单信息，带重试机制

    参数:
        symbol: 交易对符号
        max_retries: 最大重试次数

    返回:
        一个列表，每个元素是一个字典，包含挂单信息
    """
    # 重试机制
    retry_count = 0
    backoff_time = 0.5  # 初始等待时间（秒）

    while retry_count <= max_retries:
        try:
            # 获取所有未成交订单
            result = spot_client.get_open_orders(symbol, pageSize=1000)

            # 检查返回结果
            if result is None or 'resultList' not in result:
                # 如果还有重试机会，等待后重试
                if retry_count < max_retries:
                    retry_count += 1
                    time.sleep(backoff_time)
                    backoff_time *= 2  # 指数退避，每次等待时间翻倍
                    logging.warning(f"获取未成交订单返回无效结果，第 {retry_count} 次重试")
                    continue
                else:
                    logging.error("获取未成交订单返回无效结果，重试失败")
                    return []

            orders = result['resultList']

            # 检查返回结果是否为None
            if orders is None:
                # 如果还有重试机会，等待后重试
                if retry_count < max_retries:
                    retry_count += 1
                    time.sleep(backoff_time)
                    backoff_time *= 2
                    logging.warning(f"获取未成交订单返回None，第 {retry_count} 次重试")
                    continue
                else:
                    logging.error("获取未成交订单返回None，重试失败")
                    return []

            # 验证每个订单对象
            valid_orders = []
            for order in orders:
                if not isinstance(order, dict):
                    logging.warning(f"订单对象不是字典: {type(order)}")
                    continue

                order['order_id'] = order['id']

                if order['status'] in [0, 1, 3]:  # 只保留初始、新订单和部分成交的订单
                    valid_orders.append(order)

            logging.debug(f"成功获取 {len(valid_orders)} 个有效订单")
            return valid_orders

        except Exception as e:
            error_str = str(e)
            # 如果错误信息包含"API access too frequent"，则重试
            if "API access too frequent" in error_str and retry_count < max_retries:
                retry_count += 1
                # 指数退避策略，每次等待时间翻倍
                time.sleep(backoff_time)
                backoff_time *= 2
                logging.warning(f"API访问过于频繁，等待{backoff_time:.1f}秒后重试（第{retry_count}次）")
                continue
            else:
                logging.error(f"获取未成交订单失败: {error_str}")
                return []


def update_active_orders(symbol: str = symbol, force_update: bool = False, cache_duration: float = 3.0):
    """
    使用缓存机制更新活跃订单

    参数:
        symbol: 交易对符号
        force_update: 是否强制更新（忽略缓存）
        cache_duration: 缓存持续时间（秒），默认3秒
    """
    global active_orders, current_order_ids_grouped

    # 添加缓存时间控制，延长缓存时间以减少API调用
    current_time = time.time()
    if not force_update and hasattr(update_active_orders, 'last_update_time'):
        if current_time - update_active_orders.last_update_time < cache_duration:
            # 更新缓存命中统计
            if not hasattr(update_active_orders, 'cache_hit_count'):
                update_active_orders.cache_hit_count = 0
            update_active_orders.cache_hit_count += 1

            logging.debug(f"使用缓存的活跃订单数据，距离上次更新: {current_time - update_active_orders.last_update_time:.1f}秒")
            return len(active_orders)

    try:
        # 原有的更新逻辑
        pending_orders = get_pending_orders(symbol)

        # 使用集合操作优化更新过程
        new_order_ids = {order.get('order_id') or order.get('id') for order in pending_orders
                        if order.get('status') in [0, 1, 3]}

        # 计算需要删除的订单
        orders_to_remove = active_orders - new_order_ids

        # 更新活跃订单集合
        active_orders = new_order_ids

        # 更新分组字典
        new_grouped = {"buy": {}, "sell": {}}
        for order in pending_orders:
            order_id = order.get('order_id') or order.get('id')
            if order_id in new_order_ids:
                side = order['side'].lower()
                price = float(order['price'])
                if price not in new_grouped[side]:
                    new_grouped[side][price] = []
                new_grouped[side][price].append(order_id)

        current_order_ids_grouped = new_grouped

        # 更新最后更新时间和调用统计
        update_active_orders.last_update_time = current_time

        # 添加调用统计
        if not hasattr(update_active_orders, 'call_count'):
            update_active_orders.call_count = 0
            update_active_orders.cache_hit_count = 0
        update_active_orders.call_count += 1

        # 每100次调用记录一次统计信息
        if update_active_orders.call_count % 100 == 0:
            cache_hit_rate = (update_active_orders.cache_hit_count / update_active_orders.call_count) * 100
            logging.info(f"update_active_orders 调用统计: 总调用 {update_active_orders.call_count} 次, 缓存命中率 {cache_hit_rate:.1f}%")

        return len(active_orders)

    except Exception as e:
        logging.error(f"更新活跃订单失败: {str(e)}\n{traceback.format_exc()}")
        return 0


def get_market_trades(symbol: str):
    """
    获取市场成交记录并转换为适合execution_history的格式

    参数:
        symbol: 交易对符号，如"bybusdt"

    返回:
        列表，每个元素为元组(timestamp, price, inventory)
    """
    try:
        # 使用spot_market.get_trades获取成交记录
        trades_df = spot_market.get_trades(symbol)

        # 如果返回的DataFrame为空，返回空列表
        if trades_df.empty:
            logging.warning(f"获取{symbol}成交记录失败或无成交记录")
            return []

        # 获取当前资产
        if symbol[-4:] == 'usdt':
            coin = symbol[:-4]
            current_assets = get_assets(coin)
        else:
            coin = symbol[:-3]
            current_assets = get_assets(coin)
        # 计算实际库存
        current_inventory = current_assets - INITIAL_COIN_INVENTORY
        if abs(current_inventory) < 0.0001:
            current_inventory = 0.0

        # 转换为execution_history需要的格式
        execution_records = []

        for _, row in trades_df.iterrows():
            # 确保必要的字段存在
            if 'ctime' in row and 'price' in row:
                # 将ctime从毫秒转换为秒
                timestamp = row['ctime'] / 1000
                price = float(row['price'])

                # 创建元组并添加到结果列表
                execution_records.append((timestamp, price, current_inventory))

        # 按时间戳排序
        execution_records.sort(key=lambda x: x[0])

        logging.debug(f"已获取{len(execution_records)}条市场成交记录")
        return execution_records
    except Exception as e:
        logging.error(f"获取市场成交记录时发生错误: {str(e)}\n{traceback.format_exc()}")
        return []


class BybMarketMaker:
    def __init__(self,
                 base_order_size=1.0,  # 基础订单量
                 num_levels=50,  # 订单簿层数
                 inventory_limit=200000.0,  # 库存限制
                 max_inventory=500000.0,  # 最大库存量
                 price_precision=4,  # 价格精度
                 size_precision=3,  # 数量精度
                 total_side_qty=2500000,  # 单边做市总量
                 risk_aversion=0.7,  # 风险厌恶系数
                 front_levels_limit=1000.0):  # 前10档总量限制（USDT）
        """初始化做市商参数"""

        # 库存历史文件路径
        self.inventory_history_file = 'inventory_history.json'

        # 基础参数
        # 获取当前资产
        if symbol[-4:] == 'usdt':
            coin = symbol[:-4]
            api_inventory = get_assets(coin) # 获取API原始资产
        else:
            coin = symbol[:-3]
            api_inventory = get_assets(coin) # 获取API原始资产
        # 计算实际库存（使用更严格的浮点数比较）
        original_value = api_inventory - INITIAL_COIN_INVENTORY

        # 检查是否非常接近初始库存
        if abs(original_value) < 0.0001:
            self.inventory = 0.0  # 如果差值非常小，说明实际库存应为0
            logging.info(f"API资产与初始库存差值很小: {original_value}，初始化库存为0")
        elif abs(original_value - api_inventory) < 0.0001:
            # 如果原始差值接近API资产值，说明可能没有正确减去初始库存
            self.inventory = 0.0
            logging.warning(
                f"警告: 原始差值 {original_value} 接近API资产 {api_inventory}，可能没有正确减去初始库存，强制设置为0")
        else:
            self.inventory = original_value  # 当前库存（减去初始库存）
            logging.info(
                f"API资产: {api_inventory}, 初始库存: {INITIAL_COIN_INVENTORY}, 差值: {original_value}, 初始化库存为: {self.inventory}")
        self.inventory_adjustment = 0.0  # 库存理论调整量（不直接修改inventory，而是影响策略）
        self.last_inventory_check = time.time()  # 上次检查库存的时间
        self.inventory_check_interval = 10  # 检查库存的最小间隔（秒）
        self.mid_price = None  # 中间价
        self.pnl = 0.0  # 当前盈亏
        self.base_order_size = float(base_order_size)
        self.num_levels = int(num_levels)
        self.price_step = 0.0001  # 固定价格步长
        self.inventory_limit = float(inventory_limit)
        self.max_inventory = float(max_inventory)
        self.price_precision = int(price_precision)
        self.size_precision = int(size_precision)
        self.total_side_qty = float(total_side_qty)
        self.front_levels_limit = float(front_levels_limit)  # 前10档总量限制

        # 风险控制参数
        self.risk_aversion = risk_aversion
        self.max_leverage = 8.0  # 最大杠杆
        self.adapt_window = 200  # 自适应窗口大小
        self.position_penalty = 0.0
        self.dynamic_inventory_limit = inventory_limit

        # 库存管理参数
        self.inventory_target = 0.0  # 目标库存水平
        self.inventory_speed = 0.1  # 库存调整速度
        self.inventory_penalty = 0.2  # 库存惩罚因子
        self.inventory_deviation_limit = 0.2  # 库存偏离限制
        self.inventory_reversion_speed = 0.15  # 库存回归速度
        self.inventory_penalty_factor = 0.2  # 库存惩罚因子

        # 风险窗口参数
        self.inventory_risk_window = 50
        self.execution_window = 50
        self.order_imbalance_window = 50
        self.service_window = 50
        self.orderbook_window = 50
        self.orderflow_window = 50
        self.dynamic_risk_window = 50
        self.adaptation_threshold = 20

        # 风险敏感度参数
        self.inventory_risk_aversion = 0.2
        self.execution_risk_aversion = 0.2
        self.depth_aversion = 0.2
        self.liquidity_aversion = 0.2
        self.risk_sensitivity = 0.2
        self.adaptation_rate = 0.1
        self.spread_sensitivity = 0.2
        self.spread_adjustment = 0.1
        self.orderflow_sensitivity = 0.2

        # 服务成本参数
        self.service_cost = 0.0001

        # 历史数据初始化
        self.pnl_history = []  # 盈亏历史
        self.mid_price_history = []  # 中间价历史
        self.vol_history = []  # 波动率历史
        self.recent_prices = []  # 最近价格
        self.execution_history = []  # 成交历史
        self.inventory_risk_history = []  # 库存风险历史
        self.execution_risk_history = []  # 执行风险历史
        self.imbalance_history = []  # 订单失衡历史
        self.imbalance_ma = []  # 订单失衡移动平均
        self.depth_history = []  # 深度历史
        self.liquidity_history = []  # 流动性历史
        self.service_history = []  # 服务成本历史
        self.information_history = []  # 信息不对称历史
        self.orderbook_history = []  # 订单簿历史
        self.orderflow_history = []  # 订单流历史
        self.risk_history = []  # 风险历史
        self.adaptation_history = []  # 自适应历史
        self.spread_history = []  # 价差历史
        self.price_history = []  # 价格历史

        # 初始化成交历史
        self.execution_history = get_market_trades(symbol)
        self.last_execution_update = time.time()  # 上次更新成交历史的时间
        self.execution_update_interval = 60  # 更新成交历史的最小间隔（秒）

        # 加载库存历史数据
        self.inventory_history = self.load_inventory_history()  # 库存历史

        # 订单簿状态
        self.orderbook = {'bids': [], 'asks': []}
        self.update_orderbook(symbol)  # 初始化订单簿
        self.last_fill_price = None
        self.unfilled_quote_counter = {}
        self.inventory_cost = 0.0
        self.last_unfilled_quotes = None
        self.last_orderbook_update = time.time()  # 上次更新订单簿的时间
        self.orderbook_update_interval = 5  # 更新订单簿的最小间隔（秒）

        # 趋势分析参数
        self.trend_window = 100  # 趋势判断窗口
        self.trend_threshold = 0.01  # 趋势判断阈值
        self.trend_direction = 0  # 趋势方向
        self.price_ma = []  # 价格移动平均
        self.trend_strength = 0.0  # 趋势强度
        self.volatility_ma = []  # 波动率移动平均
        self.price_slope = 0.0  # 价格斜率

        # 趋势相关参数
        self.trend_inventory_factor = 0.15  # 趋势库存调整因子
        self.volatility_threshold = 0.01  # 波动率阈值
        self.slope_threshold = 0.0005  # 斜率阈值
        self.volatility_window = 20  # 波动率计算窗口
        self.slope_window = 30  # 斜率计算窗口

        # 订单控制参数
        self.min_order_size = 100.0  # 最小挂单量
        self.max_order_size = 100000.0  # 最大挂单量
        self.order_size_decay = 0.95  # 挂单量衰减因子
        self.depth_factor = 0.3  # 深度影响因子

        # 价差控制参数
        self.min_spread = 0.0002  # 最小价差
        self.max_spread = 0.001  # 最大价差

    def update_orderbook(self, symbol=symbol):
        """使用 byex.spot.market 的 get_orderbook 方法更新订单簿"""
        try:
            # 获取订单簿数据
            asks_df, bids_df = spot_market.get_orderbook(symbol)

            # 转换为我们需要的格式: [(price, qty), ...]
            asks = list(zip(asks_df["asks_price"].astype(float), asks_df["asks_qty"].astype(float)))
            bids = list(zip(bids_df["bids_price"].astype(float), bids_df["bids_qty"].astype(float)))

            # 更新订单簿
            self.orderbook = {'bids': bids, 'asks': asks}

            # 更新中间价（如果有买卖盘数据）
            if bids and asks:
                best_bid = bids[0][0]
                best_ask = asks[0][0]
                self.mid_price = (best_bid + best_ask) / 2

            # 更新时间戳
            self.last_orderbook_update = time.time()

            logging.debug(f"订单簿已更新: {len(bids)} 买单, {len(asks)} 卖单")
            return True
        except Exception as e:
            logging.error(f"更新订单簿失败: {str(e)}\n{traceback.format_exc()}")
            return False

    async def _calculate_inventory_risk(self):
        """计算库存风险 (基于Guéant et al., 2012)"""
        if len(self.inventory_history) < self.inventory_risk_window:
            return 0.0

        # 从库存历史中提取库存值
        inventory_values = self._extract_inventory_values(self.inventory_history[-self.inventory_risk_window:])

        # 计算库存波动率
        recent_inventory = np.array(inventory_values)
        inventory_volatility = np.std(recent_inventory) / (np.mean(abs(recent_inventory)) + 1e-8)

        # 计算库存风险
        inventory_risk = self.inventory_risk_aversion * inventory_volatility * abs(self.inventory)
        self.inventory_risk_history.append(inventory_risk)

        return inventory_risk

    async def _calculate_execution_risk(self):
        """计算执行风险 (基于Cartea & Jaimungal, 2015)"""
        # 由于没有执行历史数据，返回默认值
        if len(self.execution_history) < self.execution_window:
            return 0.0

        # 计算执行成本
        recent_executions = np.array(self.execution_history[-self.execution_window:])
        execution_costs = np.diff(recent_executions, axis=0)
        execution_risk = self.execution_risk_aversion * np.std(execution_costs)

        self.execution_risk_history.append(execution_risk)
        return execution_risk

    async def _calculate_order_imbalance(self):
        """计算订单失衡 (基于Avellaneda & Stoikov, 2008)"""
        if len(self.orderbook['bids']) == 0 or len(self.orderbook['asks']) == 0:
            return 0.0

        # 计算买卖盘深度
        bid_depth = sum(size for _, size in self.orderbook['bids'])
        ask_depth = sum(size for _, size in self.orderbook['asks'])
        total_depth = bid_depth + ask_depth

        if total_depth == 0:
            return 0.0

        # 计算订单失衡
        imbalance = (bid_depth - ask_depth) / total_depth
        self.imbalance_history.append(imbalance)

        # 计算移动平均
        if len(self.imbalance_history) >= self.order_imbalance_window:
            ma = np.mean(self.imbalance_history[-self.order_imbalance_window:])
            self.imbalance_ma.append(ma)

        return imbalance

    async def _calculate_depth_risk(self):
        """计算订单簿深度风险 (基于Cartea & Jaimungal, 2015)"""
        if len(self.orderbook['bids']) == 0 or len(self.orderbook['asks']) == 0:
            return 1.0

        # 计算买卖盘深度
        bid_depth = sum(size for _, size in self.orderbook['bids'])
        ask_depth = sum(size for _, size in self.orderbook['asks'])
        total_depth = bid_depth + ask_depth

        if total_depth == 0:
            return 1.0

        # 计算深度风险
        depth_ratio = min(bid_depth, ask_depth) / max(bid_depth, ask_depth)
        depth_risk = 1.0 - self.depth_aversion * depth_ratio

        self.depth_history.append(depth_risk)
        return depth_risk

    async def _calculate_liquidity_risk(self):
        """计算流动性风险 (基于Garman, 1976)"""
        if len(self.orderbook['bids']) == 0 or len(self.orderbook['asks']) == 0:
            return 1.0

        # 计算买卖盘流动性
        bid_liquidity = sum(size for _, size in self.orderbook['bids'])
        ask_liquidity = sum(size for _, size in self.orderbook['asks'])
        total_liquidity = bid_liquidity + ask_liquidity

        if total_liquidity == 0:
            return 1.0

        # 计算流动性风险
        liquidity_ratio = min(bid_liquidity, ask_liquidity) / total_liquidity
        liquidity_risk = 1.0 - self.liquidity_aversion * liquidity_ratio

        self.liquidity_history.append(liquidity_risk)
        return liquidity_risk

    async def _calculate_service_cost(self):
        """计算做市商服务成本 (基于Stoll, 1978，添加时间衰减，值域范围)"""
        if len(self.execution_history) < self.service_window:
            return self.service_cost

        # 获取最近的成交记录
        recent_executions = self.execution_history[-self.service_window:]
        service_costs = []

        # 计算时间衰减权重（使用较慢的衰减）
        time_weights = np.exp(-0.05 * np.arange(len(recent_executions)))
        time_weights = time_weights / np.sum(time_weights)

        for i, (_, price, _) in enumerate(recent_executions):
            if self.mid_price is not None:
                # 基础成本计算（只考虑价格偏离）
                base_cost = abs(price - self.mid_price) / self.mid_price

                # 应用时间衰减
                final_cost = base_cost * time_weights[i]

                service_costs.append(final_cost)

        if not service_costs:
            return self.service_cost

        # 使用加权平均计算最终服务成本
        weighted_avg_cost = np.average(service_costs, weights=time_weights)

        # 应用平滑处理（更保守的平滑）
        if len(self.service_history) > 0:
            last_cost = self.service_history[-1]
            smoothed_cost = 0.8 * last_cost + 0.2 * weighted_avg_cost
        else:
            smoothed_cost = weighted_avg_cost

        # 确保服务成本在更保守的范围内
        min_cost = self.service_cost * 0.7  # 最小不低于基础成本的70%
        max_cost = self.service_cost * 2  # 最大不超过基础成本的200%
        final_cost = max(min_cost, min(max_cost, smoothed_cost))

        self.service_history.append(final_cost)
        return final_cost

    async def _calculate_information_asymmetry(self):
        """计算信息不对称程度 (基于O'Hara, 1995)"""
        if len(self.orderbook['bids']) == 0 or len(self.orderbook['asks']) == 0:
            return 0.0

        # 计算买卖盘价差
        best_bid = self.orderbook['bids'][0][0]
        best_ask = self.orderbook['asks'][0][0]
        spread = best_ask - best_bid

        if self.mid_price is None:
            return 0.0

        # 计算信息不对称指标
        spread_ratio = spread / self.mid_price
        imbalance = await self._calculate_order_imbalance()

        # 结合价差和订单失衡计算信息不对称
        information_asymmetry = spread_ratio * (1 + abs(imbalance))
        self.information_history.append(information_asymmetry)

        return information_asymmetry

    async def _calculate_orderbook_dynamics(self):
        """计算订单簿动态 (基于Foucault et al., 2005)"""
        if len(self.orderbook_history) < self.orderbook_window:
            return 0.0

        # 计算订单簿变化率
        recent_changes = []
        for i in range(1, len(self.orderbook_history)):
            prev = self.orderbook_history[i - 1]
            curr = self.orderbook_history[i]
            change = abs(curr - prev) / (prev + 1e-8)
            recent_changes.append(change)

        if not recent_changes:
            return 0.0

        # 计算订单簿动态指标
        dynamics = np.mean(recent_changes)
        self.orderbook_history.append(dynamics)

        return dynamics

    async def _calculate_orderflow(self):
        """计算订单流 (基于Cont et al., 2010)"""
        # 由于没有执行历史数据，返回默认值
        if len(self.orderflow_history) > 0:
            # 如果有历史数据，使用最后一个值
            return self.orderflow_history[-1]
        else:
            # 否则使用默认值
            return 0.0

    async def _calculate_dynamic_risk(self):
        """计算动态风险 (基于Baldacci et al., 2021)

        参数:
            log_output: 是否输出日志，默认为False
        """
        if len(self.inventory_history) < 2 or self.inventory is None:
            return 0.0

        # 从库存历史中提取库存值
        inventory_values = self._extract_inventory_values(self.inventory_history[-self.dynamic_risk_window:])

        # 计算库存波动率
        recent_inventory = np.array(inventory_values)
        inventory_volatility = np.std(recent_inventory) / (np.mean(abs(recent_inventory)) + 1e-8)

        # 计算价格波动率
        if len(self.price_history) >= self.dynamic_risk_window:
            recent_prices = np.array(self.price_history[-self.dynamic_risk_window:])
            price_volatility = np.std(recent_prices) / np.mean(recent_prices)
        else:
            price_volatility = 0.0

        # 计算综合风险
        risk = self.risk_sensitivity * (inventory_volatility + price_volatility)
        self.risk_history.append(risk)

        return risk

    async def _calculate_adaptation_factor(self):
        """计算自适应因子 (基于Cartea et al., 2021)"""
        if len(self.risk_history) < self.adaptation_threshold:
            return 1.0

        # 计算风险趋势
        recent_risk = np.array(self.risk_history[-self.adaptation_threshold:])
        risk_trend = np.mean(np.diff(recent_risk))

        # 计算自适应因子
        adaptation = 1.0 - self.adaptation_rate * abs(risk_trend)
        self.adaptation_history.append(adaptation)

        return max(0.5, min(1.5, adaptation))

    async def _calculate_inventory_balance(self):
        """计算库存平衡调整 (基于Guéant et al., 2012)"""
        if self.inventory is None:
            return 0.0

        # 直接使用当前库存值（已经是API库存与初始库存的差值）
        current_inventory = self.inventory

        # 计算库存偏离
        inventory_deviation = current_inventory - self.inventory_target

        # 计算调整量
        adjustment = -self.inventory_speed * inventory_deviation

        # 应用惩罚因子
        if abs(inventory_deviation) > self.inventory_limit:
            adjustment *= (1 + self.inventory_penalty)

        # 不输出日志，减少日志量
        return adjustment

    async def _calculate_spread_adjustment(self, risk_factor=None):
        """计算价差调整 (基于Avellaneda & Stoikov, 2008)

        参数:
            risk_factor: 可选的风险因子，如果提供则直接使用，否则重新计算
        """
        if len(self.spread_history) < 2 or self.inventory is None:
            return 0.0

        # 直接使用当前库存值（已经是API库存与初始库存的差值）
        current_inventory = self.inventory

        # 计算库存影响
        inventory_ratio = current_inventory / self.max_inventory
        inventory_impact = self.spread_sensitivity * inventory_ratio

        # 计算风险影响
        if risk_factor is None:
            risk_factor = await self._calculate_dynamic_risk()
        risk_impact = self.spread_adjustment * risk_factor

        # 计算总调整
        adjustment = inventory_impact + risk_impact
        self.spread_history.append(adjustment)

        return adjustment

    async def _calculate_orderflow_impact(self):
        """计算订单流影响 (基于Cartea & Jaimungal, 2015)"""
        if len(self.orderflow_history) < self.orderflow_window:
            return 0.0

        # 计算订单流趋势
        recent_flow = np.array(self.orderflow_history[-self.orderflow_window:])
        flow_trend = np.mean(recent_flow)

        # 计算影响因子
        impact = self.orderflow_sensitivity * flow_trend

        return impact

    async def _update_dynamic_limits(self):
        """更新动态限制 (综合多个模型)"""
        # 重置库存调整量
        self.inventory_adjustment = 0.0

        # 计算各项指标
        dynamic_risk = await self._calculate_dynamic_risk()
        adaptation_factor = await self._calculate_adaptation_factor()
        inventory_balance = await self._calculate_inventory_balance()
        spread_adjustment = await self._calculate_spread_adjustment(risk_factor=dynamic_risk)
        orderflow_impact = await self._calculate_orderflow_impact()

        # 检查上次获取库存的时间，确保有最小间隔
        current_time = time.time()
        time_since_last_check = current_time - self.last_inventory_check

        # 直接使用当前库存值（已经是API库存与初始库存的差值）
        current_inventory = self.inventory

        # 计算库存偏离
        inventory_deviation = abs(current_inventory) / self.max_inventory

        # 如果库存偏离过大，计算调整量但不直接修改库存
        adjustment = 0.0
        if inventory_deviation > self.inventory_deviation_limit:
            # 计算理论上需要调整的量
            adjustment = -np.sign(current_inventory) * self.inventory_reversion_speed * self.max_inventory
            # 只有当距离上次检查超过最小间隔时才获取最新库存
            if time_since_last_check >= self.inventory_check_interval:
                self.update_inventory(coin=symbol[:-4])
                self.last_inventory_check = current_time

        # 保存调整量供策略使用
        self.inventory_adjustment = adjustment

        # 计算综合调整
        total_adjustment = (
                inventory_balance * adaptation_factor +
                spread_adjustment * (1 + dynamic_risk) +
                orderflow_impact
        )

        # 更新动态限制
        self.dynamic_inventory_limit = max(
            self.inventory_limit * 0.5,
            min(
                self.inventory_limit * 1.5,
                self.inventory_limit * (1 + total_adjustment)
            )
        )

        # 确保库存不超过限制
        if abs(current_inventory) > self.dynamic_inventory_limit:
            # 计算理论上的新库存值（确保为正值）
            theoretical_inventory = abs(np.sign(current_inventory) * self.dynamic_inventory_limit)
            # 计算理论上的调整量
            limit_adjustment = theoretical_inventory - abs(current_inventory)
            # 更新调整量（取更大的调整量，确保限制生效）
            if abs(limit_adjustment) > abs(self.inventory_adjustment):
                self.inventory_adjustment = limit_adjustment
            # 只有当距离上次检查超过最小间隔时才获取最新库存
            if time_since_last_check >= self.inventory_check_interval:
                self.update_inventory(coin=symbol[:-4])
                self.last_inventory_check = current_time
                logging.debug(f"已更新库存，间隔: {time_since_last_check:.1f}秒")

        # 更新历史数据
        self.risk_history.append(dynamic_risk)
        self.adaptation_history.append(adaptation_factor)
        self.spread_history.append(spread_adjustment)
        self.orderflow_history.append(orderflow_impact)

    async def _calculate_position_penalty(self):
        """计算仓位惩罚系数 (基于Avellaneda & Stoikov, 2008)"""
        # 直接使用当前库存值（已经是API库存与初始库存的差值）
        current_inventory = self.inventory

        # 计算仓位比例
        position_ratio = abs(current_inventory) / (self.dynamic_inventory_limit + 1e-8)

        # 计算库存风险
        inventory_risk = await self._calculate_inventory_risk()

        # 计算执行风险
        execution_risk = await self._calculate_execution_risk()

        # 计算综合风险惩罚
        risk_penalty = (inventory_risk + execution_risk) * self.risk_aversion

        # 使用更陡峭的惩罚函数
        self.position_penalty = tanh(position_ratio ** 2) * self.risk_aversion * 1.5 + risk_penalty

        # 波动率调整
        if len(self.vol_history) > 5:
            vol_factor = np.mean(self.vol_history[-5:]) * 150
            self.position_penalty *= 1 + vol_factor

        # 接近限制时更快增加惩罚
        if position_ratio > 0.5:
            self.position_penalty *= 1.5

    async def estimate_volatility(self, recent_prices):
        """估计价格波动率"""
        return np.std(recent_prices) if len(recent_prices) >= 2 else 0.0

    async def update_mid_price(self, fill_price):
        """更新中间价"""
        self.mid_price = fill_price

    async def _calculate_order_size(self, base_size, level, is_bid):
        """计算订单量，确保不会出现0挂单量，并引入随机性"""
        # 基础衰减
        decayed_size = base_size * (self.order_size_decay ** level)

        # 确保最小挂单量
        min_size = max(self.min_order_size, self.base_order_size)
        decayed_size = max(min_size, decayed_size)

        # 考虑市场深度影响
        if len(self.orderbook['bids']) > 0 and len(self.orderbook['asks']) > 0:
            total_depth = sum(size for _, size in self.orderbook['bids']) + sum(
                size for _, size in self.orderbook['asks'])
            depth_ratio = total_depth / (self.total_side_qty * 2)
            depth_adjustment = 1.0 + self.depth_factor * (1 - depth_ratio)
            decayed_size *= depth_adjustment

        # 考虑仓位影响
        inventory_ratio = abs(self.inventory) / self.max_inventory
        if inventory_ratio > 0.5:
            if (is_bid and self.inventory > 0) or (not is_bid and self.inventory < 0):
                decayed_size *= 0.5
            else:
                decayed_size *= 1.5

        # 添加随机性，使每个订单的大小略有不同
        # 对于买单和卖单使用不同的随机因子
        if is_bid:
            random_factor = 1.0 + random.uniform(-0.08, 0.08)  # 买单随机浮动正负8%
        else:
            random_factor = 1.0 + random.uniform(-0.08, 0.08)  # 卖单随机浮动正负8%

        decayed_size *= random_factor

        # 确保在合理范围内
        decayed_size = min(max(decayed_size, min_size), self.max_order_size)

        return round(decayed_size, self.size_precision)

    async def generate_layered_quotes(self):
        """生成分层报价"""
        # 设置最低服务成本
        current_service_cost = await self._calculate_service_cost()
        self.service_cost = max(0.0001, current_service_cost)

        if self.mid_price is None:
            return [], []

        # 更新动态参数
        await self._update_dynamic_limits()
        await self._calculate_position_penalty()

        # 创建两种不同的权重：
        # 1. 前十档使用递增权重（由小到大）
        # 2. 其余档位使用递减权重（由大到小）
        front_levels = np.arange(1, 11)  # 前十档
        back_levels = np.arange(11, self.num_levels + 1)  # 剩余档位

        # 前十档权重递增（使用指数增长函数，增长更加明显）
        # 使用幂函数来实现更陡岭的增长
        # 第一档的权重非常小，然后快速增长
        power = 2.0  # 幂指数，降低为2.0使档位权重增长更平缓
        min_weight = 0.05  # 第一档的最小权重增加到5%

        # 使用幂函数计算权重，确保第一档权重为min_weight，第十档权重为1
        normalized_levels = (front_levels - 1) / 9.0  # 归一化到[0,1]范围
        front_weights = min_weight + (1 - min_weight) * (normalized_levels ** power)

        # 剩余档位权重递减（使用指数衰减函数）
        # 确保与前十档的最后一个权重连续
        # 降低衰减率，使当前档位权重更高
        decay_rate = 0.03  # 降低衰减率，使档位权重衰减更缓慢
        back_weights = np.exp(-decay_rate * (back_levels - 10))

        # 合并权重并归一化
        weights = np.zeros(self.num_levels)
        weights[:10] = front_weights
        if len(back_levels) > 0:  # 确保有剩余档位
            weights[10:] = back_weights
        weights /= weights.sum()  # 归一化确保总和为1

        # 计算目标挂单量，买卖双方不对称
        # 买单和卖单的数量比例随机浮动，确保不完全一样
        # 买单比例设置为更高，确保买单前二十档更厚
        bid_ratio = 1.2 + random.uniform(-0.05, 0.05)  # 买单比例基准值提高到20%，浮动正负5%
        ask_ratio = 0.8 + random.uniform(-0.05, 0.05)  # 卖单比例基准值降低到20%，浮动正负5%

        # 计算目标挂单量
        target_bid_sizes = self.total_side_qty * weights * bid_ratio
        target_ask_sizes = self.total_side_qty * weights * ask_ratio

        # 计算前10档的总量（USDT）
        front_bid_total = sum(target_bid_sizes[:10] * self.mid_price)
        front_ask_total = sum(target_ask_sizes[:10] * self.mid_price)

        # 如果前10档总量超过限制，按比例缩减
        if front_bid_total > self.front_levels_limit:
            scale_factor = self.front_levels_limit / front_bid_total
            logging.info(
                f"bid前10档总量 {front_bid_total:.2f} USDT 超过限制 {self.front_levels_limit:.2f} USDT，按比例 {scale_factor:.4f} 缩减")

            # 只缩减前10档的量
            target_bid_sizes[:10] *= scale_factor

            # 验证缩减后的总量
            new_front_bid_total = sum(target_bid_sizes[:10] * self.mid_price)
            logging.info(f"缩减后bid前10档总量: {new_front_bid_total:.2f}")

        if front_ask_total > self.front_levels_limit:
            scale_factor = self.front_levels_limit / front_ask_total
            logging.info(
                f"ask前10档总量 {front_ask_total:.2f} USDT 超过限制 {self.front_levels_limit:.2f} USDT，按比例 {scale_factor:.4f} 缩减")

            # 只缩减前10档的量
            target_ask_sizes[:10] *= scale_factor

            # 验证缩减后的总量
            new_front_ask_total = sum(target_ask_sizes[:10] * self.mid_price)
            logging.info(f"缩减后ask前10档总量: {new_front_ask_total:.2f}")

        bids = []
        asks = []

        # 根据库存调整量计算报价偏移
        inventory_bias = 0.0
        if abs(self.inventory_adjustment) > 0.001:  # 如果有显著的调整量
            # 将调整量转换为价格偏移百分比，降低影响系数，从0.01降低到0.005
            # 使用非线性函数(tanh)来限制大库存时的偏移量增长
            normalized_adjustment = self.inventory_adjustment / self.max_inventory
            # 使用tanh函数使大值趋于平缓，小值保持线性
            dampened_adjustment = tanh(normalized_adjustment * 2) * 0.005
            inventory_bias = dampened_adjustment
            logging.debug(
                f"库存调整量 {self.inventory_adjustment:.4f} (归一化: {normalized_adjustment:.6f}) 转换为价格偏移: {inventory_bias:.6f}")

        # 设置一档价差范围，确保在 1 个 tick size 到千二之间
        min_tick_spread = self.price_step  # 最小价差为 1 个 tick size
        max_permille_ratio = 0.002  # 最大价差比例为千二（0.2%）

        # 计算当前中间价下的千二价差
        permille_spread_value = self.mid_price * max_permille_ratio

        # 确保一档价差在合理范围内：不小于1个tick size，不大于千二
        min_spread = max(min_tick_spread, min(permille_spread_value, self.max_spread))

        # 创建不同档位的价格步长因子，使价差随档位增加而增加
        # 前十档使用幂函数增长，后续档位使用线性增长
        price_step_factors = np.zeros(self.num_levels)

        # 前十档使用幂函数增长，使价差由小到大
        front_power = 1.3  # 幂指数
        front_levels = np.arange(1, 11)

        # 计算前十档的价格步长因子
        # 第一档使用计算好的最小价差，确保在 1 个 tick size 到千二之间
        # 后续档位使用幂函数增长，但确保第一档价差符合要求
        raw_factors = np.power(front_levels, front_power)

        # 将第一档因子调整为确保使用计算好的最小价差
        first_level_factor = min_spread / self.price_step

        # 调整前十档因子，使第一档使用计算好的最小价差
        scaling_factor = first_level_factor / raw_factors[0]
        price_step_factors[:10] = raw_factors * scaling_factor

        # 特别处理第二档，确保与第一档的价差不会过大
        if self.num_levels >= 2:
            # 将第二档的因子调整为第一档因子加上1.2倍的tick size
            price_step_factors[1] = price_step_factors[0] + 1.2

        # 记录第一档的实际价差
        first_level_spread = price_step_factors[0] * self.price_step

        # 再次验证一档价差是否在要求范围内
        if first_level_spread < min_tick_spread:
            logging.warning(f"一档价差 {first_level_spread:.6f} 小于最小值 {min_tick_spread:.6f}，调整为最小值")
            price_step_factors[0] = min_tick_spread / self.price_step
        elif first_level_spread > permille_spread_value:
            logging.warning(f"一档价差 {first_level_spread:.6f} 大于最大值 {permille_spread_value:.6f}，调整为最大值")
            price_step_factors[0] = permille_spread_value / self.price_step

        # 确保其他档位的因子也进行相应调整
        if price_step_factors[0] != first_level_factor:
            # 重新计算缩放因子
            scaling_factor = price_step_factors[0] / raw_factors[0]
            price_step_factors[1:10] = raw_factors[1:] * scaling_factor

        # 后续档位使用线性增长
        if self.num_levels > 10:
            back_levels = np.arange(11, self.num_levels + 1)
            # 从第11档开始，每档位增加1.2倍的tick size
            price_step_factors[10:] = price_step_factors[9] + 1.2 * (back_levels - 10)

        # 生成各层报价
        # 先计算最大允许的价差（千二）
        max_permille_ratio = 0.002  # 最大价差比例为千二（0.2%）
        max_permille_spread = self.mid_price * max_permille_ratio  # 当前中间价下的千二价差

        # 设置随机种子，确保每次生成的随机序列不同
        random.seed(int(time.time()))

        for i in range(self.num_levels):
            # 计算报价价格，并考虑库存调整偏移
            service_cost_factor = 1 + self.service_cost

            # 特殊处理第一档和第二档
            if i == 0:
                # 第一档使用精确计算的价差，不添加随机浮动
                random_factor = 1.0
                dynamic_step = self.price_step * price_step_factors[i] * service_cost_factor * random_factor

                # 第一档价格计算
                # 对于第一档，降低库存偏移的影响，使用更小的系数
                first_level_inventory_bias = inventory_bias * 0.5  # 第一档使用一半的库存偏移
                bid_price = self.mid_price * (1 - dynamic_step - first_level_inventory_bias)
                ask_price = self.mid_price * (1 + dynamic_step + first_level_inventory_bias)

                # 保存第一档价格供后续使用
                first_bid_price = bid_price
                first_ask_price = ask_price

            elif i == 1:
                # 第二档直接使用千二价差，确保不会超过千二
                max_permille_ratio = 0.002  # 最大价差比例为千二（0.2%）
                max_permille_spread = self.mid_price * max_permille_ratio

                # 直接从第一档价格计算第二档价格，确保至少相差1个tick_size
                # 使用随机的价差，使档位之间的价差更加自然
                price_diff_ticks_bid = max(1, random.choices([1, 2, 3], weights=[0.4, 0.4, 0.2], k=1)[0])
                price_diff_ticks_ask = max(1, random.choices([1, 2, 3], weights=[0.4, 0.4, 0.2], k=1)[0])

                # 直接从第一档价格计算第二档价格，确保至少相差多个tick_size
                bid_price = round(first_bid_price - self.price_step * price_diff_ticks_bid, self.price_precision)
                ask_price = round(first_ask_price + self.price_step * price_diff_ticks_ask, self.price_precision)

                # 计算二档价差
                second_ask_level_spread = ask_price - first_ask_price
                second_bid_level_spread = first_bid_price - bid_price

                # 如果二档价差超过千二，调整为千二价差
                if second_ask_level_spread > max_permille_spread:
                    logging.warning(f"二档ask价差与一档相差 {second_ask_level_spread:.6f} 超过千二 {max_permille_spread:.6f}，进行调整")
                    # 调整买卖价格，使价差等于千二
                    ask_price = round(first_ask_price + max_permille_spread, self.price_precision)
                if second_bid_level_spread > max_permille_spread:
                    logging.warning(f"二档bid价差与一档相差 {second_bid_level_spread:.6f} 超过千二 {max_permille_spread:.6f}，进行调整")
                    # 调整买卖价格，使价差等于千二
                    bid_price = round(first_bid_price - max_permille_spread, self.price_precision)

                # 再次确保与第一档的价差不小于1个tick_size
                if first_bid_price - bid_price < self.price_step:
                    bid_price = round(first_bid_price - self.price_step, self.price_precision)
                if ask_price - first_ask_price < self.price_step:
                    ask_price = round(first_ask_price + self.price_step, self.price_precision)
            else:
                # 其他档位添加小的随机浮动
                random_factor = 1.0 + random.uniform(-0.01, 0.01)  # 添加正负1%的小随机浮动
                dynamic_step = self.price_step * price_step_factors[i] * service_cost_factor * random_factor

                # 库存调整量为正时，需要减少库存，因此降低买单价格、提高卖单价格
                # 库存调整量为负时，需要增加库存，因此提高买单价格、降低卖单价格
                # 对于其他档位，使用递减的库存偏移影响，越靠后的档位影响越小
                level_decay = 0.9 ** i  # 每档位影响衰减10%
                level_inventory_bias = inventory_bias * level_decay
                bid_price = self.mid_price * (1 - dynamic_step - level_inventory_bias)
                ask_price = self.mid_price * (1 + dynamic_step + level_inventory_bias)

            # 确保价格为正且合理
            min_price = self.mid_price * 0.5  # 最低价格为中间价的50%
            max_price = self.mid_price * 1.5  # 最高价格为中间价的150%

            bid_price = max(min_price, min(bid_price, self.mid_price))
            ask_price = min(max_price, max(ask_price, self.mid_price))

            # 价格精度处理
            bid_price = round(bid_price, self.price_precision)
            ask_price = round(ask_price, self.price_precision)

            # 第二档已经在前面特殊处理过，这里只需要处理第三档及以后的档位
            if i > 1:
                # 计算与前一档位的价差
                prev_bid_price = bids[-1][0]
                prev_ask_price = asks[-1][0]

                # 确保买单价格单调递减，且档位之间的价差至少为1个tick_size
                if bid_price >= prev_bid_price or prev_bid_price - bid_price < self.price_step:
                    # 如果价格不单调或价差小于1个tick_size，直接调整为前一档位价格减去2个tick_size
                    # 使用2个tick_size可以避免舍入误差导致价差小于1个tick_size
                    bid_price = round(prev_bid_price - self.price_step * 2, self.price_precision)

                # 确保卖单价格单调递增，且档位之间的价差至少为1个tick_size
                if ask_price <= prev_ask_price or ask_price - prev_ask_price < self.price_step:
                    # 如果价格不单调或价差小于1个tick_size，直接调整为前一档位价格加上2个tick_size
                    # 使用2个tick_size可以避免舍入误差导致价差小于1个tick_size
                    ask_price = round(prev_ask_price + self.price_step * 2, self.price_precision)

            # 计算挂单量，并考虑库存调整
            # 如果需要减少库存（调整量为正），增加卖单量、减少买单量
            # 如果需要增加库存（调整量为负），增加买单量、减少卖单量
            inventory_size_factor = 1.0
            if abs(self.inventory_adjustment) > 0.001:
                # 计算数量调整因子，最多调整正负30%
                inventory_size_factor = 1.0 + min(0.3, abs(self.inventory_adjustment / self.max_inventory))

            if self.inventory_adjustment > 0:  # 需要减少库存
                bid_size = await self._calculate_order_size(target_bid_sizes[i] / inventory_size_factor, i, True)
                ask_size = await self._calculate_order_size(target_ask_sizes[i] * inventory_size_factor, i, False)
            elif self.inventory_adjustment < 0:  # 需要增加库存
                bid_size = await self._calculate_order_size(target_bid_sizes[i] * inventory_size_factor, i, True)
                ask_size = await self._calculate_order_size(target_ask_sizes[i] / inventory_size_factor, i, False)
            else:  # 不需要调整
                bid_size = await self._calculate_order_size(target_bid_sizes[i], i, True)
                ask_size = await self._calculate_order_size(target_ask_sizes[i], i, False)

            bids.append((bid_price, bid_size))
            asks.append((ask_price, ask_size))

        # 在应用数量调整前，只检查一档价差，其他档位不限制
        if bids and asks:
            # 只检查一档价差
            best_bid = bids[0][0]
            best_ask = asks[0][0]
            spread = best_ask - best_bid

            # 最终检查所有档位的价格单调性和最小价差
            for i in range(1, min(len(bids), len(asks))):
                bid_price, bid_size = bids[i]
                ask_price, ask_size = asks[i]

                # 确保与前一档的价差不小于1个tick_size
                prev_bid_price = bids[i - 1][0]
                prev_ask_price = asks[i - 1][0]

                # 检查并调整买单价格
                if prev_bid_price - bid_price < self.price_step:
                    new_bid_price = round(prev_bid_price - self.price_step, self.price_precision)
                    bids[i] = (new_bid_price, bid_size)

                # 检查并调整卖单价格
                if ask_price - prev_ask_price < self.price_step:
                    new_ask_price = round(prev_ask_price + self.price_step, self.price_precision)
                    asks[i] = (new_ask_price, ask_size)

            # 再次检查所有档位的价差，确保至少为1个tick_size
            for i in range(1, min(len(bids), len(asks))):
                bid_price, bid_size = bids[i]
                ask_price, ask_size = asks[i]
                prev_bid_price = bids[i - 1][0]
                prev_ask_price = asks[i - 1][0]

                bid_diff = prev_bid_price - bid_price
                ask_diff = ask_price - prev_ask_price

                # 如果价差小于1个tick_size，强制调整价格
                if bid_diff < self.price_step:
                    new_bid_price = round(prev_bid_price - self.price_step, self.price_precision)
                    bids[i] = (new_bid_price, bid_size)

                if ask_diff < self.price_step:
                    new_ask_price = round(prev_ask_price + self.price_step, self.price_precision)
                    asks[i] = (new_ask_price, ask_size)

            # 如果一档价差超过千二，调整买卖价格
            if spread > max_permille_spread:
                logging.warning(f"一档价差 {spread:.6f} 超过千二 {max_permille_spread:.6f}，进行调整")

                # 计算中间价
                mid_price = (best_bid + best_ask) / 2

                # 调整买卖价格，使价差等于千二
                new_best_bid = round(mid_price - max_permille_spread / 2, self.price_precision)
                new_best_ask = round(mid_price + max_permille_spread / 2, self.price_precision)

                # 更新买卖单价格
                bid_size = bids[0][1]
                ask_size = asks[0][1]
                bids[0] = (new_best_bid, bid_size)
                asks[0] = (new_best_ask, ask_size)

        # 确保单边做市总量不超过total_side_qty，并且买卖双方总量不完全相同
        total_bid = sum(s for _, s in bids)
        total_ask = sum(s for _, s in asks)

        # 计算调整比例，并引入小的随机浮动
        bid_target = self.total_side_qty * (1.0 + random.uniform(-0.05, 0.05))  # 买单目标总量浮动正负5%
        ask_target = self.total_side_qty * (1.0 + random.uniform(-0.05, 0.05))  # 卖单目标总量浮动正负5%

        # 确保买卖单目标总量不同
        if abs(bid_target - ask_target) < self.total_side_qty * 0.02:  # 如果差异小于2%
            ask_target += self.total_side_qty * 0.03  # 增加3%的差异

        # 计算调整比例
        bid_scale = min(1.0, bid_target / (total_bid + 1e-8))
        ask_scale = min(1.0, ask_target / (total_ask + 1e-8))

        # 应用调整
        bids = [(price, round(size * bid_scale, self.size_precision)) for price, size in bids]
        asks = [(price, round(size * ask_scale, self.size_precision)) for price, size in asks]

        # 确保买卖单不会交叉（买单最高价低于卖单最低价）
        if bids and asks:
            best_bid = max([price for price, _ in bids])
            best_ask = min([price for price, _ in asks])

            # 如果买单最高价大于等于卖单最低价，调整价格
            if best_bid >= best_ask:
                # 检测到买卖单价格交叉

                # 计算中间价
                mid_price = (best_bid + best_ask) / 2

                # 计算一档价差，确保在 1 个 tick size 到千二之间
                min_tick_spread = self.price_step  # 最小价差为 1 个 tick size
                max_permille_ratio = 0.002  # 最大价差比例为千二（0.2%）
                permille_spread_value = mid_price * max_permille_ratio

                # 确保一档价差在合理范围内
                min_price_gap = max(min_tick_spread, min(permille_spread_value, self.max_spread))

                # 调整买卖价格
                new_best_bid = round(mid_price - min_price_gap / 2, self.price_precision)
                new_best_ask = round(mid_price + min_price_gap / 2, self.price_precision)

                # 确保调整后的价格有效（买价低于卖价）
                if new_best_bid >= new_best_ask:
                    # 如果仍然交叉，进一步增加价差
                    # 使用千二价差作为备选方案
                    backup_spread = mid_price * max_permille_ratio
                    new_best_bid = round(mid_price - backup_spread / 2, self.price_precision)
                    new_best_ask = round(mid_price + backup_spread / 2, self.price_precision)

                # 更新买单价格
                bid_prices = [price for price, _ in bids]
                max_bid_idx = bid_prices.index(best_bid)
                bid_sizes = [size for _, size in bids]
                bids[max_bid_idx] = (new_best_bid, bid_sizes[max_bid_idx])

                # 更新卖单价格
                ask_prices = [price for price, _ in asks]
                min_ask_idx = ask_prices.index(best_ask)
                ask_sizes = [size for _, size in asks]
                asks[min_ask_idx] = (new_best_ask, ask_sizes[min_ask_idx])

                # 重新排序买卖单
                bids = sorted(bids, key=lambda x: -x[0])  # 买单按价格降序
                asks = sorted(asks, key=lambda x: x[0])  # 卖单按价格升序

        # 不直接覆盖 self.orderbook，而是将生成的报价保存到单独的变量
        self.generated_quotes = {'bids': bids, 'asks': asks}

        # 简化报价日志
        logging.debug(f"当前中间价: {self.mid_price:.4f}, 买单档位数: {len(bids)}, 卖单档位数: {len(asks)}")
        # 只在DEBUG级别记录详细报价
        if logging.getLogger().getEffectiveLevel() <= logging.DEBUG:
            logging.debug("=== 买单报价 ===")
            for i, (price, size) in enumerate(bids[:5]):  # 只记录前5档
                logging.debug(f"买单档位 {i + 1}: 价格={price:.4f}, 数量={size:.3f}")

            logging.debug("=== 卖单报价 ===")
            for i, (price, size) in enumerate(asks[:5]):  # 只记录前5档
                logging.debug(f"卖单档位 {i + 1}: 价格={price:.4f}, 数量={size:.3f}")

        # 记录价差信息
        if bids and asks:
            best_bid = bids[0][0]
            best_ask = asks[0][0]
            spread = best_ask - best_bid

            # 检查一档价差是否在要求范围内
            max_permille_ratio = 0.002  # 最大价差比例为千二（0.2%）
            max_permille_spread = self.mid_price * max_permille_ratio  # 当前中间价下的千二价差

            # 最后一次检查并强制调整一档价差
            if spread > max_permille_spread:
                # 最终检查并强制调整一档价差

                # 计算中间价
                mid_price = (best_bid + best_ask) / 2

                # 调整买卖价格，使价差等于千二
                new_best_bid = round(mid_price - max_permille_spread / 2, self.price_precision)
                new_best_ask = round(mid_price + max_permille_spread / 2, self.price_precision)

                # 更新买卖单价格
                bid_size = bids[0][1]
                ask_size = asks[0][1]
                bids[0] = (new_best_bid, bid_size)
                asks[0] = (new_best_ask, ask_size)

        return bids, asks

    async def show_quotes(self, top_n=50, bids=None, asks=None):
        """显示订单簿

        参数:
            top_n: 显示的最大档位数
            bids: 可选的买单列表，如果不提供则使用 self.orderbook['bids']
            asks: 可选的卖单列表，如果不提供则使用 self.orderbook['asks']
        """
        # 如果没有提供买卖单，则使用订单簿数据
        if bids is None:
            bids = self.orderbook['bids']
        if asks is None:
            asks = self.orderbook['asks']

        # 在显示前只对一档进行价差调整，其他档位不限制
        bids_copy = bids.copy()
        asks_copy = asks.copy()

        # 计算千二价差
        max_permille_ratio = 0.002  # 最大价差比例为千二（0.2%）
        max_permille_spread = self.mid_price * max_permille_ratio  # 当前中间价下的千二价差

        # 只对一档进行价差调整
        if len(bids_copy) > 0 and len(asks_copy) > 0:
            bid_price, bid_size = bids_copy[0]
            ask_price, ask_size = asks_copy[0]
            level_spread = ask_price - bid_price

            # 如果一档价差超过千二，调整买卖价格
            if level_spread > max_permille_spread:
                # 计算中间价
                mid_price_level = (bid_price + ask_price) / 2

                # 调整买卖价格，使价差等于千二
                new_bid_price = round(mid_price_level - max_permille_spread / 2, self.price_precision)
                new_ask_price = round(mid_price_level + max_permille_spread / 2, self.price_precision)

                # 更新价格
                bids_copy[0] = (new_bid_price, bid_size)
                asks_copy[0] = (new_ask_price, ask_size)

        # 只确保档位之间的价格单调性，不对档位之间的价差进行特殊处理
        # 确保所有档位的价格单调性
        for i in range(1, min(len(bids_copy), len(asks_copy))):
            prev_bid_price = bids_copy[i - 1][0]
            prev_ask_price = asks_copy[i - 1][0]
            curr_bid_price, curr_bid_size = bids_copy[i]
            curr_ask_price, curr_ask_size = asks_copy[i]

            # 确保买单价格单调递减且价差至少为1个tick_size
            if curr_bid_price >= prev_bid_price or prev_bid_price - curr_bid_price < self.price_step:
                # 如果价格不单调或价差小于1个tick_size，调整为前一档位价格减去1个tick_size
                new_bid_price = round(prev_bid_price - self.price_step, self.price_precision)
                bids_copy[i] = (new_bid_price, curr_bid_size)

            # 确保卖单价格单调递增且价差至少为1个tick_size
            if curr_ask_price <= prev_ask_price or curr_ask_price - prev_ask_price < self.price_step:
                # 如果价格不单调或价差小于1个tick_size，调整为前一档位价格加上1个tick_size
                new_ask_price = round(prev_ask_price + self.price_step, self.price_precision)
                asks_copy[i] = (new_ask_price, curr_ask_size)

        bid_data = bids_copy[:top_n]
        ask_data = asks_copy[:top_n]

        bid_data = sorted(bid_data, key=lambda x: -x[0])
        ask_data = sorted(ask_data, key=lambda x: x[0])

        logging.info(f"\n{'买价':>12} {'买量':>10} | {'卖价':>10} {'卖量':>10}")
        logging.info("-" * 48)

        # 只记录前10档到日志，避免日志过大
        display_n = min(10, top_n)
        for i in range(display_n):
            bid_price, bid_size = bid_data[i] if i < len(bid_data) else ('', '')
            ask_price, ask_size = ask_data[i] if i < len(ask_data) else ('', '')

            bid_price_str = f"{bid_price:.4f}" if bid_price != '' else ''
            bid_size_str = f"{bid_size:.3f}" if bid_size != '' else ''
            ask_price_str = f"{ask_price:.4f}" if ask_price != '' else ''
            ask_size_str = f"{ask_size:.3f}" if ask_size != '' else ''

            logging.info(f"{bid_price_str:>12} {bid_size_str:>10} | {ask_price_str:>10} {ask_size_str:>10}")

    async def _update_trend_analysis(self):
        """更新趋势分析"""
        if len(self.recent_prices) < self.trend_window:
            return

        # 计算移动平均 (使用指数加权移动平均)
        prices = np.array(self.recent_prices[-self.trend_window:])
        ma = np.mean(prices)
        self.price_ma.append(ma)

        # 计算价格斜率 (使用更短的时间窗口)
        if len(self.recent_prices) >= self.slope_window:
            recent_prices = np.array(self.recent_prices[-self.slope_window:])
            x = np.arange(len(recent_prices))
            slope, _ = np.polyfit(x, recent_prices, 1)
            self.price_slope = slope / recent_prices[0]  # 归一化斜率

        # 计算波动率 (使用更短的时间窗口)
        if len(self.recent_prices) >= self.volatility_window:
            recent_prices = np.array(self.recent_prices[-self.volatility_window:])
            volatility = np.std(recent_prices) / np.mean(recent_prices)
            self.volatility_ma.append(volatility)

        # 判断趋势方向 (更严格的判断条件)
        if len(self.volatility_ma) > 0:
            current_vol = self.volatility_ma[-1]
            if self.price_slope > self.slope_threshold and current_vol < self.volatility_threshold:
                self.trend_direction = 1
                self.trend_strength = min(1.0, self.price_slope / self.slope_threshold)
            elif self.price_slope < -self.slope_threshold and current_vol < self.volatility_threshold:
                self.trend_direction = -1
                self.trend_strength = min(1.0, abs(self.price_slope) / self.slope_threshold)
            else:
                self.trend_direction = 0
                self.trend_strength = 0.0

    async def _calculate_trend_inventory_adjustment(self):
        """计算基于趋势的库存调整"""
        if self.trend_direction == 0 or self.mid_price is None or len(self.volatility_ma) == 0:
            return 0.0

        # 计算基础调整量 (考虑波动率影响)
        current_vol = self.volatility_ma[-1]
        vol_factor = max(0.3, 1 - current_vol / self.volatility_threshold)  # 更保守的波动率调整
        base_adjustment = self.trend_inventory_factor * self.trend_strength * vol_factor

        # 根据趋势方向和波动率调整
        if self.trend_direction == 1:  # 上升趋势
            # 在低波动率且斜率较大时增加库存
            if current_vol < self.volatility_threshold and self.price_slope > self.slope_threshold:
                return base_adjustment * 1.1  # 更保守的调整
            else:
                return base_adjustment
        else:  # 下降趋势
            # 在低波动率且斜率较大时减少库存
            if current_vol < self.volatility_threshold and self.price_slope < -self.slope_threshold:
                return -base_adjustment * 1.1  # 更保守的调整
            else:
                return -base_adjustment

    async def cancel_all_orders(self, symbol: str = symbol):
        """取消所有未成交订单"""
        try:
            orders_to_cancel = list(active_orders)
            spot_client.cancel_all_orders_by_symbol(symbol)
            for order_id in orders_to_cancel:
                active_orders.remove(order_id)
                logging.info(f"成功取消订单: {order_id}")
            active_orders.clear()
            # 清空按价格分组的订单ID字典
            current_order_ids_grouped["buy"] = {}
            current_order_ids_grouped["sell"] = {}
        except Exception as e:
            logging.error(f"批量取消订单时发生错误: {str(e)}\n{traceback.format_exc()}")

    def save_inventory_history(self):
        """保存库存历史到JSON文件"""
        try:
            # 库存历史数据应该全部是 (timestamp, inventory) 格式
            inventory_data = []
            for item in self.inventory_history:
                if isinstance(item, tuple) or isinstance(item, list):
                    inventory_data.append(item)

            # 转换为可序列化的格式
            serializable_data = []
            for timestamp, inventory in inventory_data:
                serializable_data.append({
                    'timestamp': timestamp,
                    'datetime': datetime.fromtimestamp(timestamp).strftime('%Y-%m-%d %H:%M:%S'),
                    'inventory': float(inventory)
                })

            # 保存到JSON文件
            with open(self.inventory_history_file, 'w') as f:
                json.dump(serializable_data, f, indent=2)

            logging.debug(f"库存历史已保存到 {self.inventory_history_file}")
            return True
        except Exception as e:
            logging.error(f"保存库存历史时发生错误: {str(e)}\n{traceback.format_exc()}")
            return False

    def load_inventory_history(self):
        """从JSON文件加载库存历史"""
        try:
            if not os.path.exists(self.inventory_history_file):
                logging.debug(f"库存历史文件 {self.inventory_history_file} 不存在，将创建新的历史记录")
                return []

            with open(self.inventory_history_file, 'r') as f:
                data = json.load(f)

            # 转换为程序内部使用的格式 [(timestamp, inventory), ...]
            inventory_history = [(item['timestamp'], item['inventory']) for item in data]
            logging.debug(f"已从 {self.inventory_history_file} 加载 {len(inventory_history)} 条库存历史记录")
            return inventory_history
        except Exception as e:
            logging.error(f"加载库存历史时发生错误: {str(e)}\n{traceback.format_exc()}")
            return []

    def _extract_inventory_values(self, history_items=None):
        """从库存历史中提取库存值

        参数:
            history_items: 库存历史数据，如果为 None，则使用 self.inventory_history

        返回:
            库存值列表，只包含数值部分
        """
        if history_items is None:
            history_items = self.inventory_history

        inventory_values = []
        for item in history_items:
            if isinstance(item, tuple) or isinstance(item, list):
                # 如果是元组格式 (timestamp, inventory)
                # 确保库存值为正数（负值表示库存减少）
                inventory_value = item[1]
                inventory_values.append(inventory_value)
        return inventory_values

    def calculate_actual_inventory(self, api_inventory=None, coin='usdt'):
        """计算实际库存，从 API 资产中减去初始库存

        参数:
            api_inventory: 从 API 获取的原始资产值，如果为 None 则自动获取
            coin: 货币类型，默认为 'usdt'

        返回:
            实际库存值（原始资产 - 初始库存）
        """
        try:
            # 如果没有提供 API 资产值，则获取
            if api_inventory is None:
                api_inventory = get_assets(coin)

            # 获取初始库存量
            initial_inventory = INITIAL_USDT_INVENTORY if coin.lower() == 'usdt' else INITIAL_COIN_INVENTORY

            # 计算实际库存（API资产 - 初始库存）
            # 先计算原始差值
            original_value = api_inventory - initial_inventory

            # 检查是否非常接近初始库存（考虑浮点数精度问题）
            if abs(original_value) < 0.0001:
                # 如果差值非常小，说明实际库存应为0
                actual_inventory = 0.0
                logging.info(f"API资产与初始库存差值很小: {original_value}，返回0作为实际库存")
            elif abs(original_value - api_inventory) < 0.0001:
                # 如果原始差值接近API资产值，说明可能没有正确减去初始库存
                actual_inventory = 0.0
                logging.warning(
                    f"警告: 原始差值 {original_value} 接近API资产 {api_inventory}，可能没有正确减去初始库存，强制返回0")
            else:
                logging.debug(f"原始库存计算值: {original_value}")

            logging.debug(f"API资产: {api_inventory}, 初始库存: {initial_inventory}, 实际库存: {original_value}")

            return original_value
        except Exception as e:
            logging.error(f"计算实际库存时发生错误: {str(e)}\n{traceback.format_exc()}")
            # 如果计算失败，返回原始 API 库存或当前库存
            return api_inventory if api_inventory is not None else self.inventory

    def update_inventory(self, new_inventory=None, coin='usdt'):
        """更新库存并记录历史

        如果不提供new_inventory参数，则从交易所API获取最新库存并计算实际库存
        如果提供new_inventory参数，则使用该值更新库存（仅用于测试或特殊情况）
        """
        try:
            # 如果没有提供新的库存值，则从 API 获取并计算实际库存
            if new_inventory is None:
                api_inventory = get_assets(coin)
                # 计算实际库存（减去初始库存）
                new_inventory = self.calculate_actual_inventory(api_inventory, coin)

            # 更新当前库存
            old_inventory = self.inventory
            self.inventory = float(new_inventory)

            # 记录库存变化
            if old_inventory != self.inventory:
                logging.debug(
                    f"库存已更新: {old_inventory} -> {self.inventory}, 变化量: {self.inventory - old_inventory}")

            # 添加到历史记录，包含时间戳
            current_time = time.time()
            self.inventory_history.append((current_time, self.inventory))

            # 每次更新后保存历史记录
            self.save_inventory_history()

            return self.inventory
        except Exception as e:
            logging.error(f"更新库存时发生错误: {str(e)}\n{traceback.format_exc()}")
            return self.inventory

    def update_execution_history(self, symbol=symbol):
        """更新成交历史

        从交易所API获取最新成交记录并更新execution_history
        """
        try:
            # 获取最新的成交记录
            new_records = get_market_trades(symbol)

            if not new_records:
                logging.debug("未获取到新的成交记录")
                return

            # 如果execution_history为空，直接使用新记录
            if not self.execution_history:
                self.execution_history = new_records
                logging.debug(f"初始化成交历史，共{len(new_records)}条记录")
                return

            # 获取最后一条记录的时间戳
            last_timestamp = self.execution_history[-1][0]

            # 筛选出比最后一条记录更新的记录
            new_records = [record for record in new_records if record[0] > last_timestamp]

            if new_records:
                # 将新记录添加到历史记录中
                self.execution_history.extend(new_records)

                # 如果历史记录过长，可以裁剪
                max_history_length = 1000  # 设置一个合理的最大长度
                if len(self.execution_history) > max_history_length:
                    self.execution_history = self.execution_history[-max_history_length:]

                logging.debug(f"更新了{len(new_records)}条新成交记录，当前共有{len(self.execution_history)}条记录")
            else:
                logging.debug("未获取到新的成交记录")

        except Exception as e:
            logging.error(f"更新成交历史时发生错误: {str(e)}\n{traceback.format_exc()}")

    async def place_new_order(self, symbol, price, volume, side, sem):
        """挂新单，优化版本以提高效率"""
        try:
            async with sem:  # 控制并发
                try:
                    # 将side转换为小写以便于存储
                    side_lower = side.lower()

                    # 生成唯一的客户端订单ID，添加随机数确保唯一性
                    client_order_id = f'bybMm_{time.time() * 1000}_{random.randint(1000, 9999)}'

                    # 预先格式化参数，减少运行时转换
                    volume_str = str(round(float(volume), self.size_precision))
                    price_str = str(round(float(price), self.price_precision))

                    # 记录详细的下单参数，帮助诊断问题
                    logging.debug(
                        f"准备下单: {side} 侧价格 {price_str}, 数量 {volume_str}, 客户端订单ID {client_order_id}")

                    # 直接使用格式化好的参数，减少中间处理
                    params = {
                        "symbol": symbol,
                        "side": side,
                        "type": 1,
                        "volume": volume_str,
                        "price": price_str,
                        "clientOrderId": client_order_id
                    }

                    # 调用API下单
                    order_result = await spot_client.async_new_order(**params)

                    # 处理返回结果
                    if order_result and "order_id" in order_result:
                        order_id = order_result["order_id"]

                        # 添加到活跃订单集合
                        active_orders.add(order_id)

                        # 添加到按价格分组的订单ID字典，使用更高效的方式
                        if price not in current_order_ids_grouped[side_lower]:
                            current_order_ids_grouped[side_lower][price] = [order_id]
                        else:
                            current_order_ids_grouped[side_lower][price].append(order_id)

                        logging.debug(f"下单成功: {side} 侧价格 {price_str}, 数量 {volume_str}, 订单ID {order_id}")
                        return order_id

                    # 如果下单失败，记录更详细的信息
                    if order_result:
                        logging.warning(f"下单API返回无效结果: {order_result}, 参数: {params}")
                    else:
                        logging.warning(f"下单API返回None, 参数: {params}")
                    return None
                except Exception as e:
                    # 详细错误日志，帮助诊断问题
                    logging.error(f"{side} 侧价格 {price} 下单失败: {str(e)}\n{traceback.format_exc()}")
                    return None
        except Exception as e:
            # 捕获信号量相关的错误
            logging.error(f"信号量操作失败: {str(e)}\n{traceback.format_exc()}")
            # 尝试不使用信号量直接下单
            try:
                # 将side转换为小写以便于存储
                side_lower = side.lower()
                client_order_id = f'bybMm_{time.time() * 1000}_{random.randint(1000, 9999)}'
                volume_str = str(round(float(volume), self.size_precision))
                price_str = str(round(float(price), self.price_precision))
                params = {
                    "symbol": symbol,
                    "side": side,
                    "type": 1,
                    "volume": volume_str,
                    "price": price_str,
                    "clientOrderId": client_order_id
                }
                logging.info(f"尝试不使用信号量直接下单: {side} 侧价格 {price_str}")
                order_result = await spot_client.async_new_order(**params)
                if order_result and "order_id" in order_result:
                    order_id = order_result["order_id"]
                    active_orders.add(order_id)
                    if price not in current_order_ids_grouped[side_lower]:
                        current_order_ids_grouped[side_lower][price] = [order_id]
                    else:
                        current_order_ids_grouped[side_lower][price].append(order_id)
                    logging.info(f"不使用信号量下单成功: {side} 侧价格 {price_str}, 订单ID {order_id}")
                    return order_id
                return None
            except Exception as e2:
                logging.error(f"不使用信号量下单也失败: {str(e2)}")
                return None


async def initialize_orders(mm, bids, asks, sem, symbol: str=symbol):
    """首次执行时，批量铺单
    采用分批并发策略，先挂买单再挂卖单，每批并发数量有限制
    """
    global first_run

    logging.info("首次执行，批量挂单...")

    # 定义每批并发的最大订单数
    batch_size = 3

    # 先挂买单，分批并发
    logging.info(f"开始挂买单... 总共 {len(bids)} 个买单")
    for i in range(0, len(bids), batch_size):
        batch = bids[i:i + batch_size]
        batch_tasks = []
        for bid_price, bid_size in batch:
            batch_tasks.append(mm.place_new_order(symbol, bid_price, bid_size, "buy", sem))

        # 并发执行当前批次的买单
        try:
            results = await asyncio.gather(*batch_tasks, return_exceptions=True)
            # 检查每个任务的结果
            for j, result in enumerate(results):
                if isinstance(result, Exception):
                    bid_price, bid_size = batch[j]
                    logging.error(f"批次挂买单失败: 价格 {bid_price}, 数量 {bid_size}, 错误: {str(result)}")
        except Exception as e:
            logging.error(f"批次挂买单失败: {str(e)}\n{traceback.format_exc()}")

    # 再挂卖单，分批并发
    logging.info(f"开始挂卖单... 总共 {len(asks)} 个卖单")
    for i in range(0, len(asks), batch_size):
        batch = asks[i:i + batch_size]
        batch_tasks = []
        for ask_price, ask_size in batch:
            batch_tasks.append(mm.place_new_order(symbol, ask_price, ask_size, "sell", sem))

        # 并发执行当前批次的卖单
        try:
            results = await asyncio.gather(*batch_tasks, return_exceptions=True)
            # 检查每个任务的结果
            for j, result in enumerate(results):
                if isinstance(result, Exception):
                    ask_price, ask_size = batch[j]
                    logging.error(f"批次挂卖单失败: 价格 {ask_price}, 数量 {ask_size}, 错误: {str(result)}")

        except Exception as e:
            logging.error(f"批次挂卖单失败: {str(e)}\n{traceback.format_exc()}")

    # 检查实际挂单情况
    pending_orders = get_pending_orders(symbol)
    buy_orders = [order for order in pending_orders if order.get('side', '').lower() == 'buy']
    sell_orders = [order for order in pending_orders if order.get('side', '').lower() == 'sell']
    logging.info(f"挂单完成后的订单状态: {len(buy_orders)} 个买单, {len(sell_orders)} 个卖单")

    first_run = False  # 标记首次挂单完成


async def refresh_orders_with_mass_replace(mm, bids, asks, symbol:str=symbol):
    """使用cancel_and_replace函数批量撤销和挂单
    统一处理所有档位，减少API调用次数

    参数:
        mm: 做市商实例
        bids: 买单列表
        asks: 卖单列表
    """
    global first_run, current_order_ids_grouped

    if first_run:
        # 首次执行时，使用初始化函数
        await initialize_orders(mm, bids, asks, order_semaphore)
        return

    # 记录开始时间
    start_time = time.time()

    # 更新活跃订单列表，使用较短的缓存时间确保数据准确性
    valid_order_count = update_active_orders(symbol, cache_duration=2.0)
    buy_count = sum(len(ids) for ids in current_order_ids_grouped['buy'].values())
    sell_count = sum(len(ids) for ids in current_order_ids_grouped['sell'].values())
    logging.info(
        f"开始统一订单处理: 当前活跃订单 {valid_order_count} 个, 买单: {buy_count}, 卖单: {sell_count}")

    # 将买单和卖单分为前二十档和剩余档位
    front_bids = []  # 前二十档买单
    front_asks = []  # 前二十档卖单
    back_bids = []  # 剩余档位买单
    back_asks = []  # 剩余档位卖单

    # 分离前二十档和剩余档位订单，并确保不超过总档位数
    for i, (price, size) in enumerate(bids):
        if size < mm.min_order_size:
            continue
        if i < 20:
            front_bids.append((price, size))
        elif i < mm.num_levels:  # 确保不超过总档位数
            back_bids.append((price, size))

    for i, (price, size) in enumerate(asks):
        if size < mm.min_order_size:
            continue
        if i < 20:
            front_asks.append((price, size))
        elif i < mm.num_levels:  # 确保不超过总档位数
            back_asks.append((price, size))

    # 处理前二十档订单
    try:
        # 获取当前前二十档的订单ID
        front_buy_order_ids = []
        front_sell_order_ids = []

        # 优化价格排序逻辑，减少排序操作
        sorted_buy_prices = sorted(list(current_order_ids_grouped['buy'].keys()), reverse=True)[:20]
        sorted_sell_prices = sorted(list(current_order_ids_grouped['sell'].keys()))[:20]

        # 直接获取前20个价格的订单ID
        for price in sorted_buy_prices:
            front_buy_order_ids.extend(current_order_ids_grouped['buy'][price])

        for price in sorted_sell_prices:
            front_sell_order_ids.extend(current_order_ids_grouped['sell'][price])

        # 合并前二十档订单ID
        front_order_ids = front_buy_order_ids + front_sell_order_ids

        # 准备前二十档订单的mass_place参数
        front_orders = []

        # 动态决定处理顺序：当买单量大于卖单量时先处理卖单，反之先处理买单
        process_sell_first = buy_count > sell_count

        # 根据市场状况动态调整处理顺序
        if process_sell_first:
            # 先添加卖单
            for price, size in front_asks:
                if size < mm.min_order_size:
                    continue
                order_data = {
                    "price": str(round(float(price), mm.price_precision)),
                    "volume": str(round(float(size), mm.size_precision)),
                    "side": "SELL",
                    "type": 1,
                    "clientOrderId": f'bybMm_{time.time() * 1000}_{random.randint(1000, 9999)}'
                }
                front_orders.append(order_data)

            # 再添加买单
            for price, size in front_bids:
                if size < mm.min_order_size:
                    continue
                order_data = {
                    "price": str(round(float(price), mm.price_precision)),
                    "volume": str(round(float(size), mm.size_precision)),
                    "side": "BUY",
                    "type": 1,
                    "clientOrderId": f'bybMm_{time.time() * 1000}_{random.randint(1000, 9999)}'
                }
                front_orders.append(order_data)
        else:
            # 先添加买单
            for price, size in front_bids:
                if size < mm.min_order_size:
                    continue
                order_data = {
                    "price": str(round(float(price), mm.price_precision)),
                    "volume": str(round(float(size), mm.size_precision)),
                    "side": "BUY",
                    "type": 1,
                    "clientOrderId": f'bybMm_{time.time() * 1000}_{random.randint(1000, 9999)}'
                }
                front_orders.append(order_data)

            # 再添加卖单
            for price, size in front_asks:
                if size < mm.min_order_size:
                    continue
                order_data = {
                    "price": str(round(float(price), mm.price_precision)),
                    "volume": str(round(float(size), mm.size_precision)),
                    "side": "SELL",
                    "type": 1,
                    "clientOrderId": f'bybMm_{time.time() * 1000}_{random.randint(1000, 9999)}'
                }
                front_orders.append(order_data)

        # 确保撤单和挂单数量匹配
        if len(front_orders) > 0:
            # 分批处理，每批最多40个订单（买单和卖单各20个）
            batch_size = 40
            for i in range(0, len(front_orders), batch_size):
                batch_orders = front_orders[i:i + batch_size]
                batch_cancel_ids = front_order_ids[i:i + batch_size] if i < len(front_order_ids) else []

                # 确保撤单和挂单数量完全匹配
                if len(batch_orders) != len(batch_cancel_ids):
                    # 如果数量不匹配，直接截断而不是补充撤单ID
                    if len(batch_orders) > len(batch_cancel_ids):
                        # 减少挂单数量以匹配撤单ID数量
                        logging.warning(
                            f"挂单数量({len(batch_orders)})大于撤单ID数量({len(batch_cancel_ids)}), 减少挂单数量")
                        batch_orders = batch_orders[:len(batch_cancel_ids)]
                    else:
                        # 如果撤单数量大于挂单数量，截断撤单ID
                        logging.warning(
                            f"撤单ID数量({len(batch_cancel_ids)})大于挂单数量({len(batch_orders)}), 截断撤单ID")
                        batch_cancel_ids = batch_cancel_ids[:len(batch_orders)]

                # 记录批次处理信息
                logging.debug(f"处理前二十档批次: 挂单数量={len(batch_orders)}, 撤单数量={len(batch_cancel_ids)}")
                logging.debug(f"批次订单详情: {json.dumps(batch_orders, indent=2)}")

                try:
                    if batch_cancel_ids or batch_orders:
                        result = spot_client.cancel_and_replace(
                            symbol=symbol,
                            mass_place=batch_orders,
                            mass_cancel=batch_cancel_ids
                        )
                        logging.debug(f"前二十档订单替换结果: {result}")

                        # 延迟更新活跃订单状态，避免过于频繁的API调用
                        # 只在最后一个批次处理完成后更新
                        if i + batch_size >= len(front_orders):
                            update_active_orders(symbol, cache_duration=2.0)
                            # 更新买卖单计数
                            buy_count = sum(len(ids) for ids in current_order_ids_grouped['buy'].values())
                            sell_count = sum(len(ids) for ids in current_order_ids_grouped['sell'].values())
                            logging.info(f"前二十档处理完成，订单状态: 买单: {buy_count}, 卖单: {sell_count}")
                        else:
                            logging.debug(f"批次 {i//batch_size + 1} 处理完成，等待所有批次完成后统一更新状态")

                    await asyncio.sleep(0.1)
                except Exception as e:
                    if "API access too frequent" in str(e):
                        logging.warning("API访问过于频繁，等待后重试")
                        await asyncio.sleep(1.0)
                        if batch_cancel_ids or batch_orders:
                            spot_client.cancel_and_replace(
                                symbol=symbol,
                                mass_place=batch_orders,
                                mass_cancel=batch_cancel_ids
                            )
                            await asyncio.sleep(1.0)
                    else:
                        logging.error(f"处理前二十档订单时发生错误: {str(e)}")
                        raise

    except Exception as e:
        logging.error(f"处理前二十档订单时发生错误: {str(e)}")
        return

    # 检查是否需要处理剩余档位
    back_levels_update_counter += 1
    if back_levels_update_counter < BACK_LEVELS_UPDATE_INTERVAL:
        return

    # 重置计数器
    back_levels_update_counter = 0

    # 处理剩余档位订单
    try:
        # 获取当前剩余档位的订单ID
        back_buy_order_ids = []
        back_sell_order_ids = []

        # 按价格排序获取剩余档位买单ID
        sorted_buy_prices = sorted(current_order_ids_grouped['buy'].keys(), reverse=True)
        for price in sorted_buy_prices[20:]:
            if len(back_buy_order_ids) < mm.num_levels - 20:
                back_buy_order_ids.extend(current_order_ids_grouped['buy'][price])
            else:
                break

        # 按价格排序获取剩余档位卖单ID
        sorted_sell_prices = sorted(current_order_ids_grouped['sell'].keys())
        for price in sorted_sell_prices[20:]:
            if len(back_sell_order_ids) < mm.num_levels - 20:
                back_sell_order_ids.extend(current_order_ids_grouped['sell'][price])
            else:
                break

        # 合并剩余档位订单ID
        back_order_ids = back_buy_order_ids + back_sell_order_ids

        # 准备剩余档位订单的mass_place参数
        back_orders = []

        # 动态决定处理顺序：当买单量大于卖单量时先处理卖单，反之先处理买单
        process_sell_first = buy_count > sell_count

        # 根据市场状况动态调整处理顺序
        if process_sell_first:
            # 先添加卖单
            for price, size in back_asks:
                if size < mm.min_order_size:
                    continue
                order_data = {
                    "price": str(round(float(price), mm.price_precision)),
                    "volume": str(round(float(size), mm.size_precision)),
                    "side": "SELL",
                    "type": 1,
                    "clientOrderId": f'bybMm_{time.time() * 1000}_{random.randint(1000, 9999)}'
                }
                back_orders.append(order_data)

            # 再添加买单
            for price, size in back_bids:
                if size < mm.min_order_size:
                    continue
                order_data = {
                    "price": str(round(float(price), mm.price_precision)),
                    "volume": str(round(float(size), mm.size_precision)),
                    "side": "BUY",
                    "type": 1,
                    "clientOrderId": f'bybMm_{time.time() * 1000}_{random.randint(1000, 9999)}'
                }
                back_orders.append(order_data)
        else:
            # 先添加买单
            for price, size in back_bids:
                if size < mm.min_order_size:
                    continue
                order_data = {
                    "price": str(round(float(price), mm.price_precision)),
                    "volume": str(round(float(size), mm.size_precision)),
                    "side": "BUY",
                    "type": 1,
                    "clientOrderId": f'bybMm_{time.time() * 1000}_{random.randint(1000, 9999)}'
                }
                back_orders.append(order_data)

            # 再添加卖单
            for price, size in back_asks:
                if size < mm.min_order_size:
                    continue
                order_data = {
                    "price": str(round(float(price), mm.price_precision)),
                    "volume": str(round(float(size), mm.size_precision)),
                    "side": "SELL",
                    "type": 1,
                    "clientOrderId": f'bybMm_{time.time() * 1000}_{random.randint(1000, 9999)}'
                }
                back_orders.append(order_data)

        # 确保撤单和挂单数量匹配
        if len(back_orders) > 0:
            # 分批处理，每批最多100个订单
            batch_size = 100
            for i in range(0, len(back_orders), batch_size):
                batch_orders = back_orders[i:i + batch_size]
                batch_cancel_ids = back_order_ids[i:i + batch_size] if i < len(back_order_ids) else []

                # 确保撤单和挂单数量完全匹配
                if len(batch_orders) != len(batch_cancel_ids):
                    # 如果数量不匹配，直接截断而不是补充撤单ID
                    if len(batch_orders) > len(batch_cancel_ids):
                        # 减少挂单数量以匹配撤单ID数量
                        logging.warning(
                            f"挂单数量({len(batch_orders)})大于撤单ID数量({len(batch_cancel_ids)}), 减少挂单数量")
                        batch_orders = batch_orders[:len(batch_cancel_ids)]
                    else:
                        # 如果撤单数量大于挂单数量，截断撤单ID
                        logging.warning(
                            f"撤单ID数量({len(batch_cancel_ids)})大于挂单数量({len(batch_orders)}), 截断撤单ID")
                        batch_cancel_ids = batch_cancel_ids[:len(batch_orders)]

                try:
                    if batch_cancel_ids or batch_orders:
                        result = spot_client.cancel_and_replace(
                            symbol=symbol,
                            mass_place=batch_orders,
                            mass_cancel=batch_cancel_ids
                        )
                        logging.debug(f"剩余档位订单替换结果: {result}")

                        # 延迟更新活跃订单状态，避免过于频繁的API调用
                        # 只在最后一个批次处理完成后更新
                        if i + batch_size >= len(back_orders):
                            update_active_orders(symbol, cache_duration=2.0)
                            # 更新买卖单计数
                            buy_count = sum(len(ids) for ids in current_order_ids_grouped['buy'].values())
                            sell_count = sum(len(ids) for ids in current_order_ids_grouped['sell'].values())
                            logging.info(f"剩余档位处理完成，订单状态: 买单: {buy_count}, 卖单: {sell_count}")
                        else:
                            logging.debug(f"剩余档位批次 {i//batch_size + 1} 处理完成，等待所有批次完成后统一更新状态")

                    await asyncio.sleep(0.1)
                except Exception as e:
                    if "API access too frequent" in str(e):
                        logging.warning("API访问过于频繁，等待后重试")
                        await asyncio.sleep(1.0)
                        if batch_cancel_ids or batch_orders:
                            spot_client.cancel_and_replace(
                                symbol=symbol,
                                mass_place=batch_orders,
                                mass_cancel=batch_cancel_ids
                            )
                            await asyncio.sleep(1.0)
                    else:
                        logging.error(f"处理剩余档位订单时发生错误: {str(e)}")
                        raise

    except Exception as e:
        logging.error(f"处理剩余档位订单时发生错误: {str(e)}")
        return

    # 最终更新订单ID分组，强制更新以确保数据准确性
    try:
        valid_order_count = update_active_orders(symbol, force_update=True)
        buy_orders_count = sum(len(ids) for ids in current_order_ids_grouped["buy"].values())
        sell_orders_count = sum(len(ids) for ids in current_order_ids_grouped["sell"].values())
        logging.info(
            f"批量订单处理完成: {buy_orders_count} 个买单, {sell_orders_count} 个卖单, 共 {valid_order_count} 个有效订单")
    except Exception as e:
        logging.error(f"更新订单ID分组时发生错误: {str(e)}")


async def main():
    # 使用全局变量 first_run 来控制是否是首次运行
    global first_run

    # 初始化做市商
    mm = BybMarketMaker(
        base_order_size=10,
        num_levels=50,
        inventory_limit=20000.0,
        max_inventory=100000.0,
        price_precision=4,
        size_precision=2,
        risk_aversion=0.5,
        total_side_qty=500000
    )

    # 使用get_trade_price获取最新成交价作为初始价格
    try:
        # 获取所有交易对的最新成交价
        trade_prices_df = spot_market.get_trade_price()
        # 过滤出交易对的数据
        symbol_data = trade_prices_df[trade_prices_df['symbol'] == symbol]

        if not symbol_data.empty:
            # 获取最新成交价
            initial_price = float(symbol_data['price'].iloc[0])
            logging.debug(f"从 get_trade_price 获取到的初始价格: {initial_price}")
        else:
            # 如果没有找到symbol的数据，使用默认值
            initial_price = 0.1  # 使用更合理的默认值
            logging.warning(f"未找到{symbol}的成交价数据，使用默认值: {initial_price}")
    except Exception as e:
        # 如果获取失败，使用默认值
        initial_price = 0.1  # 使用更合理的默认值
        logging.error(f"获取成交价失败: {str(e)}\n{traceback.format_exc()}")
        logging.warning(f"使用默认初始价格: {initial_price}")

    # 更新中间价
    await mm.update_mid_price(initial_price)

    # 确保首次运行标志被设置
    first_run = True

    # 初始化库存和成交历史更新时间
    last_inventory_update = time.time()
    last_execution_update = time.time()
    inventory_update_interval = 180  # 增加到每180秒更新一次库存，减少API调用频率
    execution_update_interval = 90  # 增加到每90秒更新一次成交历史，减少API调用频率

    # 首次运行时取消所有订单
    try:
        await mm.cancel_all_orders()
        logging.info("成功取消所有现有订单")
    except Exception as e:
        logging.error(f"取消所有订单失败: {str(e)}\n{traceback.format_exc()}")

    # 创建订单状态检查任务
    last_order_check = time.time()
    order_check_interval = 10  # 增加到每10秒批量检查一次订单状态，减少API调用频率

    # 添加订单类型平衡检查时间
    last_balance_check = time.time()
    balance_check_interval = 120  # 增加到每120秒检查一次订单类型平衡，减少API调用频率

    # 记录连续失败次数
    consecutive_failures = 0
    max_consecutive_failures = 5

    # 记录初始价格，以便后续使用
    logging.debug(f"使用从 get_trade_price 获取的初始价格: {initial_price}")

    while True:
        try:
            # 记录循环开始时间
            current_time = time.time()

            # 批量检查活跃订单状态（每10秒检查一次，减少API调用频率）
            if current_time - last_order_check >= order_check_interval and active_orders:
                # 记录检查前的活跃订单数量
                pre_check_count = len(active_orders)
                pre_check_buy_count = sum(len(ids) for ids in current_order_ids_grouped["buy"].values())
                pre_check_sell_count = sum(len(ids) for ids in current_order_ids_grouped["sell"].values())
                logging.info(
                    f"定期检查活跃订单: {pre_check_count} 个 (买单: {pre_check_buy_count}, 卖单: {pre_check_sell_count})")

                try:
                    # 使用较长的缓存时间进行定期检查，减少API调用
                    valid_order_count = update_active_orders(symbol=symbol, cache_duration=5.0)

                    # 计算更新后的订单数量
                    post_check_count = len(active_orders)
                    post_check_buy_count = sum(len(ids) for ids in current_order_ids_grouped["buy"].values())
                    post_check_sell_count = sum(len(ids) for ids in current_order_ids_grouped["sell"].values())

                    # 只有在订单数量发生变化时才记录详细信息
                    if post_check_count != pre_check_count or post_check_buy_count != pre_check_buy_count or post_check_sell_count != pre_check_sell_count:
                        logging.info(
                            f"订单状态变化: {pre_check_count} -> {post_check_count} 个订单, 买单: {pre_check_buy_count} -> {post_check_buy_count}, 卖单: {pre_check_sell_count} -> {post_check_sell_count}")
                    else:
                        logging.debug(f"订单状态无变化: {post_check_count} 个订单 (买单: {post_check_buy_count}, 卖单: {post_check_sell_count})")
                except Exception as e:
                    logging.error(f"更新活跃订单列表失败: {str(e)}")

                last_order_check = current_time

            # 检查订单类型平衡（每120秒检查一次，减少API调用频率）
            if current_time - last_balance_check >= balance_check_interval:
                try:
                    # 使用较长的缓存时间进行平衡检查，减少API调用
                    update_active_orders(symbol=symbol, cache_duration=10.0)

                    # 计算买单和卖单数量
                    buy_orders_count = sum(len(ids) for ids in current_order_ids_grouped["buy"].values())
                    sell_orders_count = sum(len(ids) for ids in current_order_ids_grouped["sell"].values())

                    logging.info(f"订单平衡检查: {buy_orders_count}买/{sell_orders_count}卖")

                    # 如果没有任何订单，重新铺单
                    if buy_orders_count == 0 and sell_orders_count == 0:
                        logging.warning("检测到没有任何订单，将重新铺单")
                        first_run = True
                        # 清空订单记录已经在update_active_orders中完成

                    last_balance_check = current_time
                except Exception as e:
                    logging.error(f"检查订单平衡失败: {str(e)}")

            # 定期更新库存和成交历史
            # 更新库存（降低频率，减少API调用）
            if current_time - last_inventory_update >= inventory_update_interval:
                try:
                    # 从交易所API获取最新库存，不提供参数以确保使用API数据
                    mm.update_inventory(coin=symbol[:-4])
                    mm.last_inventory_check = current_time  # 更新检查时间
                    last_inventory_update = current_time
                except Exception as e:
                    logging.error(f"更新库存失败: {str(e)}")

            # 更新成交历史
            if current_time - last_execution_update >= execution_update_interval:
                mm.update_execution_history()
                last_execution_update = current_time

            # 获取当前市场价格
            if first_run:
                # 如果是首次运行，使用从 get_trade_price 获取的初始价格
                current_price = initial_price
                logging.debug(f"首次运行使用从 get_trade_price 获取的初始价格: {initial_price}")
                # first_run 变量将在 refresh_orders_random 函数中被设置为 False
            else:
                # 从交易所获取当前市场价格
                market_data = spot_market.get_ticker(symbol=symbol)
                current_price = float(market_data['last'])
                logging.debug(f"从 get_ticker 获取当前市场价格: {current_price}")

            # 更新中间价
            await mm.update_mid_price(current_price)

            # 生成报价（这是一个计算密集型操作，但不涉及API调用）
            bids, asks = await mm.generate_layered_quotes()

            # 使用优化后的批量撤单和挂单策略，前二十档变化更快，剩余档位变化较慢
            try:
                await refresh_orders_with_mass_replace(mm, bids, asks)
                # 成功刷新订单，重置连续失败计数
                consecutive_failures = 0
            except Exception as e:
                logging.error(f"刷新订单失败: {str(e)}\n{traceback.format_exc()}")
                # 增加连续失败计数
                consecutive_failures += 1

                # 如果连续失败次数超过限制，重置首次运行标志并取消所有订单
                if consecutive_failures >= max_consecutive_failures:
                    logging.warning(f"检测到连续 {consecutive_failures} 次失败，重置策略状态")
                    first_run = True
                    consecutive_failures = 0
                    try:
                        await mm.cancel_all_orders()
                        logging.info("重置后成功取消所有订单")
                    except Exception as e2:
                        logging.error(f"重置后取消订单失败: {str(e2)}")
                    # 等待一段时间再重试
                    await asyncio.sleep(1)

            # 显示当前状态（仅在日志级别为DEBUG时显示详细信息，减少日志开销）
            if logging.getLogger().getEffectiveLevel() <= logging.DEBUG:
                logging.debug("=== 生成的报价 ===")
                await mm.show_quotes(top_n=10, bids=bids, asks=asks)
                logging.debug("=== 实际订单簿 ===")
                await mm.show_quotes(top_n=10)
            else:
                # 在INFO级别只显示简要信息
                if bids and asks:
                    logging.debug(f"报价生成完成: 买单 {len(bids)} 档，卖单 {len(asks)} 档")

            # 计算当前循环执行时间
            loop_end_time = time.time()
            loop_duration = loop_end_time - current_time

            if loop_duration > 2.0:
                logging.warning(f"循环执行时间过长: {loop_duration:.3f}秒，超过2秒目标")
        except Exception as e:
            logging.error(f"主循环发生错误: {str(e)}\n{traceback.format_exc()}")
            await asyncio.sleep(1)  # 发生错误时等待更长时间


if __name__ == "__main__":
    # 创建一个新的事件循环并设置为当前线程的事件循环
    loop = asyncio.new_event_loop()
    asyncio.set_event_loop(loop)

    try:
        # 在事件循环中运行main函数
        loop.run_until_complete(main())
    except Exception as e:
        logging.error(f"主程序执行错误: {str(e)}\n{traceback.format_exc()}")
    finally:
        # 关闭事件循环
        loop.close()
